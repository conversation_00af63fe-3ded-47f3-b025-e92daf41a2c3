import traceback
from datetime import datetime, timedelta
from decimal import Decimal

from trader.base_bot import BaseBot
from trader.colored_logger import log_progress_bar
from trader.models.public_data import Candles


class BacktestingBot(BaseBot):
    """Bot para backtesting com dados históricos"""

    def __init__(self, api, strategy, report, account):
        # Desabilitar logging para backtesting
        super().__init__(api, strategy, report, account, enable_logging=False)

    def get_historical_prices(
        self, start_date: datetime, end_date: datetime, resolution: str
    ) -> Candles:
        return self.api.get_candles(self.symbol, start_date, end_date, resolution)

    def _initialize_strategy_with_historical_data(self, test_start_date: datetime, interval: int) -> None:
        """
        Inicializa a estratégia com dados históricos anteriores ao período de teste.

        Args:
            test_start_date: Data de início do teste (para buscar dados anteriores)
            interval: Intervalo em segundos usado no backtesting
        """
        # Verificar se a estratégia precisa de dados históricos
        if not hasattr(self.strategy, 'historical_data_days') or self.strategy.historical_data_days <= 0:
            print("📊 Estratégia não precisa de dados históricos para inicialização")
            return

        try:
            # Calcular período para buscar dados históricos (antes do teste)
            historical_end_date = test_start_date
            historical_start_date = historical_end_date - timedelta(days=self.strategy.historical_data_days)

            # Determinar resolução baseada no interval
            resolution = self._get_resolution_from_interval(interval)

            # Buscar candles históricos para inicialização
            historical_candles = self.get_historical_prices(
                historical_start_date, historical_end_date, resolution
            )

            # Converter preços de fechamento para Decimal
            historical_prices = [Decimal(price) for price in historical_candles.c]

            # Inicializar a estratégia com os dados históricos
            self.strategy.initialize_with_historical_data(historical_prices)

            print(f"📊 Estratégia inicializada com {len(historical_prices)} preços históricos (período: {historical_start_date.strftime('%Y-%m-%d')} a {historical_end_date.strftime('%Y-%m-%d')}, resolução: {resolution})")

        except Exception as e:
            print(f"⚠️ Não foi possível inicializar com dados históricos: {e}")
            # Continuar sem dados históricos

    def _get_resolution_from_interval(self, interval: int) -> str:
        """
        Converte interval em segundos para resolução de candles.

        Args:
            interval: Intervalo em segundos

        Returns:
            str: Resolução no formato da API (ex: '1m', '15m', '1h', etc.)
        """
        return self.INTERVAL_TO_RESOLUTION.get(interval, "1h")

    def run(self, start_date: datetime, end_date: datetime, interval: int = 60):
        """Executa o backtesting com dados históricos"""
        self.is_running = True

        # 1ª Chamada: Buscar dados históricos para inicializar a estratégia
        self._initialize_strategy_with_historical_data(start_date, interval)

        # 2ª Chamada: Buscar dados do período de teste para o loop do backtesting
        test_candles = self.get_historical_prices(
            start_date, end_date, self.INTERVAL_TO_RESOLUTION[interval]
        )

        total_candles = len(test_candles.c)
        print(f"🚀 Iniciando backtesting com {total_candles} candles do período de teste...")

        # Inicializar barra de progresso
        log_progress_bar(0.0, overwrite=False)

        for index, str_price in enumerate(test_candles.c):
            current_price = Decimal(str_price)
            try:
                timestamp = datetime.fromtimestamp(test_candles.t[index])

                # Usar método da classe base para processar dados de mercado
                self.process_market_data(current_price, timestamp)

                # Atualizar barra de progresso
                progress_percent = ((index + 1) / total_candles) * 100
                log_progress_bar(progress_percent)

            except KeyboardInterrupt:
                print("\n🛑 Bot interrompido pelo usuário")
                self.stop()
                return
            except Exception as e:
                print(f"❌ Erro no loop principal: {str(e)}")
                traceback.print_exc()

        print("\n📈 Simulação finalizada")
        self.stop()
